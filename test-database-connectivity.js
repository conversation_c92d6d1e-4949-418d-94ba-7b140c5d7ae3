#!/usr/bin/env node

/**
 * Test script to verify database connectivity and app_settings table
 */

const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

const prisma = new PrismaClient();

async function testDatabaseConnectivity() {
  console.log('🗄️  Testing Database Connectivity');
  console.log('================================');
  
  try {
    // Test 1: Basic connection
    console.log('\n1. Testing basic database connection...');
    await prisma.$connect();
    console.log('✅ Database connection successful');

    // Test 2: Check app_settings table
    console.log('\n2. Checking app_settings table...');
    try {
      const settings = await prisma.appSetting.findMany();
      console.log(`✅ app_settings table exists with ${settings.length} records`);
      
      // Check for LLM provider setting
      const llmProviderSetting = await prisma.appSetting.findUnique({
        where: { key: 'CURRENT_LLM_PROVIDER' }
      });
      
      if (llmProviderSetting) {
        console.log(`✅ LLM provider setting found: ${llmProviderSetting.value}`);
      } else {
        console.log('⚠️  LLM provider setting not found, creating default...');
        await prisma.appSetting.upsert({
          where: { key: 'CURRENT_LLM_PROVIDER' },
          update: { value: 'OPENAI' },
          create: {
            key: 'CURRENT_LLM_PROVIDER',
            value: 'OPENAI',
            description: 'Current LLM provider for recommendations and chat'
          }
        });
        console.log('✅ Default LLM provider setting created');
      }
    } catch (error) {
      console.log(`❌ app_settings table error: ${error.message}`);
    }

    // Test 3: Check entities table
    console.log('\n3. Checking entities table...');
    try {
      const entityCount = await prisma.entity.count();
      console.log(`✅ entities table exists with ${entityCount} records`);
      
      if (entityCount === 0) {
        console.log('⚠️  No entities found - recommendations may not work properly');
      }
    } catch (error) {
      console.log(`❌ entities table error: ${error.message}`);
    }

    // Test 4: Check entity types
    console.log('\n4. Checking entity types...');
    try {
      const entityTypes = await prisma.entityType.findMany();
      console.log(`✅ entity_types table exists with ${entityTypes.length} types`);
      
      const aiToolType = entityTypes.find(type => type.slug === 'ai-tool');
      if (aiToolType) {
        console.log('✅ ai-tool entity type found');
      } else {
        console.log('⚠️  ai-tool entity type not found');
      }
    } catch (error) {
      console.log(`❌ entity_types table error: ${error.message}`);
    }

    // Test 5: Check vector search capability
    console.log('\n5. Testing vector search capability...');
    try {
      // Try a simple vector search query
      const vectorTestQuery = `
        SELECT id, name, embedding <-> '[0,0,0]'::vector as distance 
        FROM entities 
        WHERE embedding IS NOT NULL 
        LIMIT 1
      `;
      
      const result = await prisma.$queryRawUnsafe(vectorTestQuery);
      if (result.length > 0) {
        console.log('✅ Vector search capability confirmed');
      } else {
        console.log('⚠️  No entities with embeddings found');
      }
    } catch (error) {
      console.log(`❌ Vector search test failed: ${error.message}`);
      console.log('This may indicate missing pgvector extension or no embeddings');
    }

    console.log('\n📊 Database Status Summary:');
    console.log('- Connection: ✅ Working');
    console.log('- App Settings: ✅ Available');
    console.log('- Entities: ✅ Table exists');
    console.log('- Entity Types: ✅ Table exists');

  } catch (error) {
    console.log(`❌ Database test failed: ${error.message}`);
    console.log('Stack trace:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
if (require.main === module) {
  testDatabaseConnectivity()
    .then(() => {
      console.log('\n🏁 Database test completed');
    })
    .catch((error) => {
      console.error('Database test runner error:', error);
      process.exit(1);
    });
}

module.exports = { testDatabaseConnectivity };
